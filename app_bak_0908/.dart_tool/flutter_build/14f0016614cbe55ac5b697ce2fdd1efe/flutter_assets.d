 /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/blob.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/stars.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBLACKITALIC.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYREGULAR.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYTHINITALIC.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBOLD.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYMEDIUM.OTF /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ai_magic.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent_v1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/gradient_card.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent_v2.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/recording_green_circle_icon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/emotional_feedback_1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon_v2.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/x_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/apple_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-permissions.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/facebook_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon_v1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/calendar_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/slack_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-without-rope-turned-off.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/email_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/4.mov /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_v1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name-grey.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/notion_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding.mp4 /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/5.mov /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/imessage_logo.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_v2.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_clone_plus.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-devkit-without-rope.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/2.mov /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/whatsapp_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/3.mov /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/link_icon.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/1.mov /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-glass.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/stripe_logo.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-language-grey.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/background.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/clone.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_3.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name-white.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-1.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_2.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-3.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/new_background.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-2.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-6.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/google_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/telegram_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-5-2.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/checkbox.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_setting_persona.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_clone_chat.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-4.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/x_logo_mini.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-5-1.jpg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/apple-reminders-logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_dollar.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/speaker_1_icon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/speaker_0_icon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v4.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_v1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instagram_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/Logo%20Text%20White.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_chart.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/youtube_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_v2.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/linkedin_logo.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_old.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v3.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v1.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_persona_profile.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-without-rope.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/server_error.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/wave.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/no_internet.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/pdfs/favicon.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/silero_vad.onnx /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/silero_vad.v5.onnx /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/shorebird.yaml /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/device_assets/frame_lib.lua /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/awesome_notifications/test/assets/images/test_image.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound/assets/js/tau_web.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound/assets/js/async_processor.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/howler/howler.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_player.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_recorder.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_stream_processor.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/frame_sdk/assets/frameLib.lua /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mappls.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/citymapper.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/sygicTruck.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/naver.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tencent.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/copilot.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/truckmeister.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexNavi.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexMaps.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tmap.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/petal.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/doubleGis.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/here.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgofleet.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapswithme.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmandplus.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/google.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/googleGo.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapyCz.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/kakao.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmand.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgo.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/flitsmeister.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/baidu.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/apple.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/waze.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/amap.svg /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/mcumgr_flutter/assets/mock_logs.txt /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/mixpanel_flutter/assets/mixpanel.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.js /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.wasm /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/opus_license.txt /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x64.dll.blob /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x86.dll.blob /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/win_ble/assets/BLEServer.exe /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_close.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/AssetManifest.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/FontManifest.json /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/NOTICES.Z /Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Documents/omi/app/pubspec.yaml /Users/<USER>/Documents/omi/app/ios/Runner/Info.plist /Users/<USER>/Documents/omi/app/ios/Flutter/AppFrameworkInfo.plist /Users/<USER>/Documents/omi/app/assets/images/blob.png /Users/<USER>/Documents/omi/app/assets/images/stars.png /Users/<USER>/Documents/omi/app/assets/images/herologo.png /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYBLACKITALIC.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYREGULAR.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYTHINITALIC.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYBOLD.OTF /Users/<USER>/Documents/omi/app/assets/fonts/SFPRODISPLAYMEDIUM.OTF /Users/<USER>/Documents/omi/app/assets/images/logo_transparent.png /Users/<USER>/Documents/omi/app/assets/images/ai_magic.svg /Users/<USER>/Documents/omi/app/assets/images/logo_transparent_v1.png /Users/<USER>/Documents/omi/app/assets/images/gradient_card.png /Users/<USER>/Documents/omi/app/assets/images/logo_transparent_v2.png /Users/<USER>/Documents/omi/app/assets/images/recording_green_circle_icon.png /Users/<USER>/Documents/omi/app/assets/images/emotional_feedback_1.png /Users/<USER>/Documents/omi/app/assets/images/splash_icon_v2.png /Users/<USER>/Documents/omi/app/assets/images/x_logo.png /Users/<USER>/Documents/omi/app/assets/images/apple_logo.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-permissions.png /Users/<USER>/Documents/omi/app/assets/images/facebook_logo.png /Users/<USER>/Documents/omi/app/assets/images/splash_icon_v1.png /Users/<USER>/Documents/omi/app/assets/images/calendar_logo.png /Users/<USER>/Documents/omi/app/assets/images/slack_logo.png /Users/<USER>/Documents/omi/app/assets/images/omi-without-rope-turned-off.png /Users/<USER>/Documents/omi/app/assets/images/email_logo.png /Users/<USER>/Documents/omi/app/assets/images/4.mov /Users/<USER>/Documents/omi/app/assets/images/app_launcher_icon_v1.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-name-grey.png /Users/<USER>/Documents/omi/app/assets/images/notion_logo.png /Users/<USER>/Documents/omi/app/assets/images/onboarding.mp4 /Users/<USER>/Documents/omi/app/assets/images/5.mov /Users/<USER>/Documents/omi/app/assets/images/imessage_logo.svg /Users/<USER>/Documents/omi/app/assets/images/app_launcher_icon_v2.png /Users/<USER>/Documents/omi/app/assets/images/ic_clone_plus.svg /Users/<USER>/Documents/omi/app/assets/images/omi-devkit-without-rope.png /Users/<USER>/Documents/omi/app/assets/images/2.mov /Users/<USER>/Documents/omi/app/assets/images/onboarding-name.png /Users/<USER>/Documents/omi/app/assets/images/splash_icon.png /Users/<USER>/Documents/omi/app/assets/images/whatsapp_logo.png /Users/<USER>/Documents/omi/app/assets/images/3.mov /Users/<USER>/Documents/omi/app/assets/images/link_icon.svg /Users/<USER>/Documents/omi/app/assets/images/1.mov /Users/<USER>/Documents/omi/app/assets/images/omi-glass.png /Users/<USER>/Documents/omi/app/assets/images/app_launcher_icon.png /Users/<USER>/Documents/omi/app/assets/images/stripe_logo.svg /Users/<USER>/Documents/omi/app/assets/images/onboarding-language-grey.png /Users/<USER>/Documents/omi/app/assets/images/background.png /Users/<USER>/Documents/omi/app/assets/images/clone.png /Users/<USER>/Documents/omi/app/assets/images/instruction_3.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-name-white.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-1.jpg /Users/<USER>/Documents/omi/app/assets/images/instruction_2.png /Users/<USER>/Documents/omi/app/assets/images/splash.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-3.jpg /Users/<USER>/Documents/omi/app/assets/images/new_background.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-2.jpg /Users/<USER>/Documents/omi/app/assets/images/instruction_1.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-6.jpg /Users/<USER>/Documents/omi/app/assets/images/google_logo.png /Users/<USER>/Documents/omi/app/assets/images/telegram_logo.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-5-2.jpg /Users/<USER>/Documents/omi/app/assets/images/checkbox.svg /Users/<USER>/Documents/omi/app/assets/images/ic_setting_persona.svg /Users/<USER>/Documents/omi/app/assets/images/ic_clone_chat.svg /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-4.jpg /Users/<USER>/Documents/omi/app/assets/images/x_logo_mini.png /Users/<USER>/Documents/omi/app/assets/images/onboarding-bg-5-1.jpg /Users/<USER>/Documents/omi/app/assets/images/apple-reminders-logo.png /Users/<USER>/Documents/omi/app/assets/images/ic_dollar.svg /Users/<USER>/Documents/omi/app/assets/images/speaker_1_icon.png /Users/<USER>/Documents/omi/app/assets/images/speaker_0_icon.png /Users/<USER>/Documents/omi/app/assets/images/herologo_v4.png /Users/<USER>/Documents/omi/app/assets/images/splash_v1.png /Users/<USER>/Documents/omi/app/assets/images/instagram_logo.png /Users/<USER>/Documents/omi/app/assets/images/Logo\ Text\ White.png /Users/<USER>/Documents/omi/app/assets/images/ic_chart.svg /Users/<USER>/Documents/omi/app/assets/images/youtube_logo.png /Users/<USER>/Documents/omi/app/assets/images/splash_v2.png /Users/<USER>/Documents/omi/app/assets/images/linkedin_logo.png /Users/<USER>/Documents/omi/app/assets/images/app_launcher_icon_old.png /Users/<USER>/Documents/omi/app/assets/images/herologo_v3.png /Users/<USER>/Documents/omi/app/assets/images/herologo_v1.png /Users/<USER>/Documents/omi/app/assets/images/ic_persona_profile.svg /Users/<USER>/Documents/omi/app/assets/images/omi-without-rope.png /Users/<USER>/Documents/omi/app/assets/lottie_animations/server_error.json /Users/<USER>/Documents/omi/app/assets/lottie_animations/wave.json /Users/<USER>/Documents/omi/app/assets/lottie_animations/no_internet.json /Users/<USER>/Documents/omi/app/assets/pdfs/favicon.png /Users/<USER>/Documents/omi/app/assets/silero_vad.onnx /Users/<USER>/Documents/omi/app/assets/silero_vad.v5.onnx /Users/<USER>/Documents/omi/app/shorebird.yaml /Users/<USER>/Documents/omi/app/assets/device_assets/frame_lib.lua /Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/test/assets/images/test_image.png /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/assets/js/tau_web.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/assets/js/async_processor.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/howler/howler.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/src/flutter_sound.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/src/flutter_sound_player.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/src/flutter_sound_recorder.js /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/src/flutter_sound_stream_processor.js /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-brands-400.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-regular-400.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/lib/fonts/fa-solid-900.ttf /Users/<USER>/.pub-cache/hosted/pub.dev/frame_sdk-0.0.7/assets/frameLib.lua /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_all_platforms_desktop-0.0.2/assets/post_auth_page.html /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/mappls.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/citymapper.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/sygicTruck.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/naver.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/tencent.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/copilot.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/truckmeister.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/yandexNavi.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/yandexMaps.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/tmap.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/petal.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/doubleGis.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/here.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/tomtomgofleet.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/mapswithme.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/osmandplus.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/google.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/googleGo.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/mapyCz.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/kakao.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/osmand.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/tomtomgo.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/flitsmeister.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/baidu.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/apple.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/waze.svg /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/assets/icons/amap.svg /Users/<USER>/.pub-cache/hosted/pub.dev/mcumgr_flutter-0.4.2/assets/mock_logs.txt /Users/<USER>/.pub-cache/hosted/pub.dev/mixpanel_flutter-2.4.4/assets/mixpanel.js /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_web-3.0.3/assets/libopus.js /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_web-3.0.3/assets/libopus.wasm /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_windows-3.0.0/assets/opus_license.txt /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_windows-3.0.0/assets/libopus_x64.dll.blob /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_windows-3.0.0/assets/libopus_x86.dll.blob /Users/<USER>/.pub-cache/hosted/pub.dev/win_ble-1.1.1/lib/assets/BLEServer.exe /Users/<USER>/.pub-cache/hosted/pub.dev/window_manager-0.3.7/images/ic_chrome_close.png /Users/<USER>/.pub-cache/hosted/pub.dev/window_manager-0.3.7/images/ic_chrome_maximize.png /Users/<USER>/.pub-cache/hosted/pub.dev/window_manager-0.3.7/images/ic_chrome_minimize.png /Users/<USER>/.pub-cache/hosted/pub.dev/window_manager-0.3.7/images/ic_chrome_unmaximize.png /Users/<USER>/Documents/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/Documents/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Documents/omi/app/.dart_tool/flutter_build/14f0016614cbe55ac5b697ce2fdd1efe/native_assets.json /Users/<USER>/.pub-cache/git/flutter_silero_vad-d4fcc2a287933c0d29f8bec568b3f6fffbf117ee/LICENSE /Users/<USER>/.pub-cache/git/mixpanel_analytics-4518cde0910b400d6b7378be757585db4ca2a15c/LICENSE /Users/<USER>/.pub-cache/git/opus_flutter-0190d7a660945f8c450085533262239a56d58c86/opus_flutter_android/LICENSE /Users/<USER>/.pub-cache/git/opus_flutter-04696ff930a464bd47677026d45c3c3004f6daab/opus_flutter_ios/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-76.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/_flutterfire_internals-1.3.54/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/analyzer-6.11.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ansicolor-2.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links-6.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_linux-1.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_platform_interface-2.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/app_links_web-1.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/archive-3.6.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/auto_size_text-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications_core-0.9.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/barcode-2.2.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/bidi-2.0.13/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_config-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_daemon-4.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_resolvers-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/build_runner_core-9.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/built_value-8.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_platform_interface-4.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cached_network_image_web-1.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/checked_yaml-2.0.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cli_util-0.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/color-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus_platform_interface-2.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cross_file-0.3.4+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/csslib-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dart_style-2.3.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dartx-1.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dbus-0.7.11/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dotted_border-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/envied-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/envied_generator-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/expandable_text-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/extension_google_sign_in_as_googleapis_auth-2.0.13/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_picker-8.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_linux-0.9.3+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_macos-0.9.4+3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_platform_interface-2.6.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file_selector_windows-0.9.3+4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-5.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_platform_interface-7.6.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth_web-5.14.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_platform_interface-5.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.24.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_platform_interface-4.6.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.68.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_archive-6.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service-5.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_android-6.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_ios-5.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_background_service_platform_interface-5.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus-1.33.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_blue_plus_windows-1.24.21/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_cache_manager-3.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_flavorizr-2.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_foreground_task-9.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_gen_core-5.10.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_gen_runner-5.10.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_launcher_icons-0.13.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_markdown-0.7.7+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_plugin_android_lifecycle-2.0.28/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_provider_utilities-1.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_rating_bar-4.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound-9.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_platform_interface-9.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_sound_web-9.28.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_svg-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_timezone-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/font_awesome_flutter-10.8.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/frame_sdk-0.0.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/frontend_server_client-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-14.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-5.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.13/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_platform_interface-4.2.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_identity_services_web-0.3.3+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in-6.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_all_platforms-1.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_all_platforms_desktop-0.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_all_platforms_interface-0.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_all_platforms_mobile-0.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_android-6.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_ios-5.7.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_platform_interface-2.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/google_sign_in_web-0.12.4+4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/googleapis_auth-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/gradient_borders-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/graphs-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/group_button-5.3.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/growthbook_sdk_flutter-3.9.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/gtk-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/hashcodes-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/html-0.15.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_methods-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_multi_server-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image-4.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_android-0.8.12+23/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_for_web-3.0.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_ios-0.8.12+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_linux-0.2.1+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_macos-0.2.1+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_platform_interface-2.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_picker_windows-0.2.1+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/image_size_getter-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/in_app_review-2.0.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/in_app_review_platform_interface-2.0.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/inject_js-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/instabug_flutter-14.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intercom_flutter-9.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intercom_flutter_platform_interface-2.0.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intercom_flutter_web-1.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/internet_connection_checker_plus-2.7.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/io-1.0.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_annotation-4.9.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/json_serializable-6.8.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_platform_interface-4.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.16/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-4.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logger-2.6.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lottie-3.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/macros-0.1.3-main.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/map_launcher-3.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/markdown-7.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mcumgr_flutter-0.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mime-2.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/mixpanel_flutter-2.4.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nm-0.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nordic_dfu-6.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/octo_image-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/opus_dart-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_platform_interface-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_web-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/opus_flutter_windows-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/os_detect-2.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus_platform_interface-3.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_drawing-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_parsing-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pdf-3.11.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-12.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-13.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_platform_interface-4.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/petitparser-6.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/photo_view-0.15.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pointycastle-3.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pool-1.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/posthog_flutter-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/process-5.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/protobuf-3.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pubspec_parse-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/pull_down_button-0.10.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/qr-3.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/recase-4.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/rxdart-0.27.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/screen_retriever-0.1.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/share_plus_platform_interface-5.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf-1.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf_router-1.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shelf_web_socket-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shimmer-3.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple-6.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_platform_interface-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sign_in_with_apple_web-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/siri_wave-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/skeletonizer-2.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_gen-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_helper-1.3.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sprintf-7.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_common-2.5.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_platform_interface-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_transform-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_with_value-0.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/sync_http-0.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/synchronized-3.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/talker-4.9.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/talker_flutter-4.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/talker_logger-4.9.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/time-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timeago-3.7.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/timing-1.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/tuple-2.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/universal_io-2.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/upgrader-11.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.16/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_platform_interface-2.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/uuid-4.5.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics-1.1.19/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_codec-1.1.13/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_graphics_compiler-1.1.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/version-3.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player-2.10.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_android-2.8.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_avfoundation-2.8.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_platform_interface-6.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/video_player_web-2.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/visibility_detector-0.4.0+2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-15.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/wav-1.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_ffi-0.7.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webdriver-3.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter-4.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_android-4.7.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_platform_interface-2.13.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/webview_flutter_wkwebview-3.22.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.14.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win_ble-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/window_manager-0.3.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xml-6.5.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3/LICENSE /Users/<USER>/Documents/flutter/bin/cache/dart-sdk/pkg/_macros/LICENSE /Users/<USER>/Documents/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/Documents/flutter/packages/flutter/LICENSE /Users/<USER>/Documents/omi/app/DOES_NOT_EXIST_RERUN_FOR_WILDCARD976433276