["/Users/<USER>/Documents/omi/app/ios/Flutter/ephemeral/flutter_lldbinit", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/Flutter.framework/Flutter", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/vm_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/isolate_snapshot_data", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/kernel_blob.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/App", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/Info.plist", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/blob.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/stars.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYSEMIBOLDITALIC.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYHEAVYITALIC.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYLIGHTITALIC.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBLACKITALIC.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYREGULAR.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYTHINITALIC.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYBOLD.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/fonts/SFPRODISPLAYMEDIUM.OTF", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ai_magic.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent_v1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/gradient_card.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/logo_transparent_v2.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/recording_green_circle_icon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/emotional_feedback_1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon_v2.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/x_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/apple_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-permissions.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/facebook_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon_v1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/calendar_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/slack_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-without-rope-turned-off.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/email_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/4.mov", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_v1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name-grey.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/notion_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding.mp4", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/5.mov", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/imessage_logo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_v2.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_clone_plus.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-devkit-without-rope.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/2.mov", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_icon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/whatsapp_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/3.mov", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/link_icon.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/1.mov", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-glass.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/stripe_logo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-language-grey.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/background.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/clone.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_3.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-name-white.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-1.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_2.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-3.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/new_background.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-2.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instruction_1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-6.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/google_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/telegram_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-5-2.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/checkbox.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_setting_persona.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_clone_chat.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-4.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/x_logo_mini.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/onboarding-bg-5-1.jpg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/apple-reminders-logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_dollar.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/speaker_1_icon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/speaker_0_icon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v4.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_v1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/instagram_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/Logo%20Text%20White.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_chart.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/youtube_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/splash_v2.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/linkedin_logo.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/app_launcher_icon_old.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v3.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/herologo_v1.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/ic_persona_profile.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/images/omi-without-rope.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/server_error.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/wave.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/lottie_animations/no_internet.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/pdfs/favicon.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/silero_vad.onnx", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/silero_vad.v5.onnx", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/shorebird.yaml", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/assets/device_assets/frame_lib.lua", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/awesome_notifications/test/assets/images/test_image.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound/assets/js/tau_web.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound/assets/js/async_processor.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/howler/howler.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_player.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_recorder.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/flutter_sound_web/src/flutter_sound_stream_processor.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-brands-400.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-regular-400.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/font_awesome_flutter/lib/fonts/fa-solid-900.ttf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/frame_sdk/assets/frameLib.lua", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/google_sign_in_all_platforms_desktop/assets/post_auth_page.html", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mappls.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/citymapper.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/sygicTruck.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/naver.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tencent.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/copilot.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/truckmeister.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexNavi.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/yandexMaps.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tmap.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/petal.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/doubleGis.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/here.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgofleet.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapswithme.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmandplus.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/google.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/googleGo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/mapyCz.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/kakao.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/osmand.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/tomtomgo.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/flitsmeister.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/baidu.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/apple.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/waze.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/map_launcher/assets/icons/amap.svg", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/mcumgr_flutter/assets/mock_logs.txt", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/mixpanel_flutter/assets/mixpanel.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.js", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_web/assets/libopus.wasm", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/opus_license.txt", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x64.dll.blob", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/opus_flutter_windows/assets/libopus_x86.dll.blob", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/win_ble/assets/BLEServer.exe", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_close.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_maximize.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_minimize.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/packages/window_manager/images/ic_chrome_unmaximize.png", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/AssetManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/FontManifest.json", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/NOTICES.Z", "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-eiuqgmlvjsdznmabmcvsudlttonl/Build/Products/Debug-dev-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json"]